'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import LoginForm from '@/components/auth/LoginForm';
import RegisterForm from '@/components/auth/RegisterForm';
import { Recycle, Leaf, Users, Heart } from 'lucide-react';

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState('login');

  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-4">
      <div className="w-full max-w-7xl grid lg:grid-cols-2 gap-8 items-center">
        {/* Left side - Branding */}
        <div className="hidden lg:block space-y-8">
          <div>
            <h1 className="text-5xl font-semibold text-[#032221] mb-4">
              <span className="text-[#01796F]">Pedi</span>
            </h1>
            <p className="text-xl text-gray-700 mb-6">
              The Future of Fashion is Circular
            </p>
            <p className="text-base text-gray-600 leading-relaxed">
              Join Kenya&apos;s first sustainable clothing exchange platform. Swap, donate, and discover pre-loved fashion while earning Pedi tokens and making a positive impact on our environment.
            </p>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div className="p-5 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <Recycle className="h-8 w-8 text-[#01796F] mx-auto mb-2" />
              <h3 className="font-medium text-[#032221] text-center">Circular Fashion</h3>
              <p className="text-sm text-gray-600 text-center">Reduce textile waste through clothing swaps</p>
            </div>
            
            <div className="p-5 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <Leaf className="h-8 w-8 text-[#01796F] mx-auto mb-2" />
              <h3 className="font-medium text-[#032221] text-center">Eco-Friendly</h3>
              <p className="text-sm text-gray-600 text-center">Lower your carbon footprint</p>
            </div>
            
            <div className="p-5 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <Users className="h-8 w-8 text-[#01796F] mx-auto mb-2" />
              <h3 className="font-medium text-[#032221] text-center">Community</h3>
              <p className="text-sm text-gray-600 text-center">Connect with like-minded fashion lovers</p>
            </div>
            
            <div className="p-5 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <Heart className="h-8 w-8 text-[#01796F] mx-auto mb-2" />
              <h3 className="font-medium text-[#032221] text-center">Give Back</h3>
              <p className="text-sm text-gray-600 text-center">Donate to local charities</p>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <h3 className="font-medium text-[#032221] mb-3">How it works:</h3>
            <ol className="space-y-3 text-sm text-gray-600">
              <li className="flex items-start gap-2">
                <span className="bg-[#01796F] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">1</span>
                List your pre-loved clothes with photos and descriptions
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-[#01796F] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">2</span>
                Browse and discover amazing pieces from other users
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-[#01796F] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">3</span>
                Swap using Pedi tokens or arrange direct exchanges
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-[#01796F] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">4</span>
                Earn tokens for sustainable actions and community participation
              </li>
            </ol>
          </div>
        </div>

        {/* Right side - Auth Forms */}
        <div className="w-full max-w-md mx-auto lg:mx-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6 bg-white border border-gray-200 rounded-lg">
              <TabsTrigger 
                value="login"
                className={`px-4 py-3 font-medium data-[state=active]:bg-[#01796F] data-[state=active]:text-white transition-all`}
              >
                Sign In
              </TabsTrigger>
              <TabsTrigger 
                value="register"
                className={`px-4 py-3 font-medium data-[state=active]:bg-[#01796F] data-[state=active]:text-white transition-all`}
              >
                Sign Up
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="login">
              <LoginForm onSwitchToRegister={() => setActiveTab('register')} />
            </TabsContent>
            
            <TabsContent value="register">
              <RegisterForm onSwitchToLogin={() => setActiveTab('login')} />
            </TabsContent>
          </Tabs>

          {/* Mobile branding */}
          <div className="lg:hidden mt-6 text-center">
            <h2 className="text-2xl font-semibold text-[#032221] mb-2">
              Welcome to <span className="text-[#01796F]">Pedi</span>
            </h2>
            <p className="text-gray-600 text-sm">
              Kenya&apos;s sustainable fashion exchange platform
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}